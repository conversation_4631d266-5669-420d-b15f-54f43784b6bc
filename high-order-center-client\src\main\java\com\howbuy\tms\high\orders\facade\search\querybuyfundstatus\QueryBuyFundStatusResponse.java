/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querybuyfundstatus;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:(查询产品状态)
 * @reason:
 * @date 2017年11月28日 上午9:15:54
 * @since JDK 1.6
 */
@Data
public class QueryBuyFundStatusResponse extends OrderSearchBaseResponse {

    /**
     * serialVersionUID:
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = -3671618386893410819L;
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 产品状态list
     */
    private List<BuyFundStatusBean> buyFundStatusList;

    /**
     * @description:(产品状态bean)
     */
    @Data
    public static class BuyFundStatusBean implements Serializable {

        /**
         * serialVersionUID:
         *
         * @since Ver 1.1
         */
        private static final long serialVersionUID = 7757051939821034685L;
        /**
         * 产品代码
         */
        private String productCode;
        /**
         * 收费类型A-前收费;B-后收费
         */
        private String shareClass;
        /**
         * 产品状态0-不可购买 1-可购买
         */
        private String buyStatus;

        /**
         * 产品购买状态,CAN_BUY:可购买,CAN_NOT_BUY:不可购买,CAN_MODIFY:可修改,CAN_NOT_MODIFY:不可修改;
         */
        private String fundBuyStatus;
        /**
         * 状态标识1-正常 2-代销不支持 3.年龄限制 4-已售罄 5-直销转代销的黑名单6-产品状态不可购买或不在预约期内
         * 8-产品参数配置有误99-其它
         */
        private String buyStatusType;
        /**
         * 说明信息
         */
        private String msg;

    }

}