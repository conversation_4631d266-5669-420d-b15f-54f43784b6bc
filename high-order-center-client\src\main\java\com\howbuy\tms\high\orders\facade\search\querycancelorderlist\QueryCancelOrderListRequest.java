/**
 * Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.facade.search.querycancelorderlist;

import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * @description:(查询高端可撤单列表)
 * <AUTHOR>
 * @date 2017年4月25日 下午1:18:55
 * @since JDK 1.6
 */
public class QueryCancelOrderListRequest extends OrderSearchBaseRequest {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 2344792007400426065L;

    private String taTradeDt;
    private String isAuth;

    /**
     * @api {dubbo} com.howbuy.tms.high.orders.facade.search.querycancelorderlist.QueryCancelOrderListFacade.execute() 查询高端可撤订单列表接口
     * @apiGroup high-order-center
     * @apiDescription 查询高端可撤订单列表
     *
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     *
     * @apiParam {String} txAcctNo 交易账号
     * @apiParam {String} disCode 分销机构
     * @apiParam {String} outletCode 网点号
     * @apiParam {String} txCode 中台交易代码
     * @apiParam {String} taTradeDt TA交易日期
     *
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.search.querycancelorderlist.QueryCancelOrderListRequest
     *{
     *    "appDt": "20170413", 
     *    "appTm": "143122", 
     *    "dataTrack": "0c7b031f-7e1f-4d7c-b1de-3eaaa735db9c", 
     *    "disCode": "HB000A001", 
     *    "operIp": "127.0.0.1", 
     *    "outletCode": "A20150120", 
     *    "pageNo": 1, 
     *    "pageSize": 20, 
     *    "advanceFlag": "1",
     *    "description": "成功",
     *    "disCode": "HB000A001",
     *    "pageNo": 0,
     *    "returnCode": "Z0000000",
     *    "taTradeDt": "20170210",
     *    "totalCount": 0,
     *    "totalPage": 0,
     *    "txAcctNo": "1100875155",
     *    "txChannel": "4",
     *    "txCode": "Z330024"
     *}
     *
     * @apiUse orderBaseResponse
     * @apiUse orderSearchBaseResponse
     *
     * @apiSuccess {CancelOrderBean} cancelOrderBean 高端可撤订单
     *
     * @apiSuccess (cancelOrderBean) {List} cancelOrderList 高端订单列表
     * @apiSuccess (cancelOrderList) {String} dealNo 客户订单号
     * @apiSuccess (cancelOrderList) {String} disCode 分销机构
     * @apiSuccess (cancelOrderList) {String} txAcctNo 交易账号
     * @apiSuccess (cancelOrderList) {String} bankAcct  银行账号
     * @apiSuccess (cancelOrderList) {String} bankCode  银行代码
     * @apiSuccess (cancelOrderList) {String} productName 产品名称
     * @apiSuccess (cancelOrderList) {String} productCode 产品代码
     * @apiSuccess (cancelOrderList) {String} paymentType 支付方式<br>01-自划款; 04-代扣款; 06-储蓄罐
     * @apiSuccess (cancelOrderList) {String} appAmt  申请金额
     * @apiSuccess (cancelOrderList) {String} appVol 申请份额
     * @apiSuccess (cancelOrderList) {String} appRatio 申请比例
     * @apiSuccess (cancelOrderList) {String} appDtm 申请日期时间
     * @apiSuccess (cancelOrderList) {String} payStatus 付款状态<br>0-无需付款;1-未付款;2-付款中;3-部分成功;4-成功;5-失败
     * @apiSuccess (cancelOrderList) {String} orderStatus 订单状态<br>1-申请成功;2-部分确认;3-确认成功;4-确认失败;5-自行撤销;6-强制取消
     * @apiSuccess (cancelOrderList) {String} taTradeDt TA交易日期
     * @apiSuccess (cancelOrderList) fee 手续费
     * @apiSuccess (cancelOrderList) mBusiCode  中台业务码<br>1120-认购; 1122-申购; 1124-赎回; 1136-基金转换; 1236-基金转投; 1142-强赎; 1143-分红;1144-强增;1145-强减;1129-修改分红方式;1260-拉杆平衡;1261-波动平衡;1262-观点平衡
     * @apiSuccess (cancelOrderList) redeemDirection  赎回去向<br>0-银行卡;1-储蓄罐
     * @apiSuccess (cancelOrderList) firstBuyFlag  首次购买标识<br>1-首次购买;2-追加购买
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.search.querycancelorderlist.QueryCancelOrderListResponse
     *
     * {
     * "highCanCancelDealOrders": [
     *        {
     *                "appAmt": 5555,
     *                "appDtm": 1492073481006,
     *                "cpAcctNo": "2017020800761370",
     *                "dealNo": "1017041316512500002002155",
     *                "description": "成功",
     *                "disCode": "HB000A001",
     *                "idNo": "321183198707210756",
     *                "orderStatus": "6",
     *                "outletCode": "H20131104",
     *                "pageNo": 0,
     *                "payStatus": "5",
     *                "paymentType": "04",
     *                "productCode": "481001",
     *                "productName": "工银瑞信核心价值混合型证券投资基金",
     *                "returnCode": "Z0000000",
     *                "taTradeDt": "20170210",
     *                "totalCount": 0,
     *                "totalPage": 0,
     *                "txAcctNo": "1100875155",
     *                "txCode": "Z330008",
     *                "fee":0.1,
     *                "mBusiCode":"1122",
     *                redeemDirection:"0"
     *        }
     *    ],
     *    "returnCode": "Z0000000",
     *    "totalCount": 0,
     *    "pageNum":1,
     *    "totalPage": 0,
     *    "txAcctNo": "1100875141"
     *
     * }
     *
     */
    public QueryCancelOrderListRequest() {
        super.setTxCode(TxCodes.QUERY_HIGH_CAN_CANCEL_DEAL_ORDER_LIST);
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public String getIsAuth() {
        return isAuth;
    }

    public void setIsAuth(String isAuth) {
        this.isAuth = isAuth;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt;
    }

}

