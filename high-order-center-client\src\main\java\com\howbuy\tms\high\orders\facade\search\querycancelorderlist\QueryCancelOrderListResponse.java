/**
 * Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querycancelorderlist;

import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description:(查询高端可撤单列表响应)
 * <AUTHOR>
 * @date 2017年4月25日 下午1:21:03
 * @since JDK 1.6
 */
public class QueryCancelOrderListResponse extends OrderSearchBaseResponse {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 907824797490448558L;
    /**
     * 是否持有好臻产品 0:没有,1:有
     */
    private String hasHzProduct = YesOrNoEnum.NO.getCode();
    /**
     * 是否持有好买香港产品  0:没有,1:有
     */
    private String hasHkProduct = YesOrNoEnum.NO.getCode();
    /**
     * 撤单列表
     */
    private List<CancelOrderBean> cancelOrderList;

    public List<CancelOrderBean> getCancelOrderList() {
        return cancelOrderList;
    }

    public String getHasHzProduct() {
        return hasHzProduct;
    }

    public void setHasHzProduct(String hasHzProduct) {
        this.hasHzProduct = hasHzProduct;
    }

    public String getHasHkProduct() {
        return hasHkProduct;
    }

    public void setHasHkProduct(String hasHkProduct) {
        this.hasHkProduct = hasHkProduct;
    }

    public void setCancelOrderList(List<CancelOrderBean> cancelOrderList) {
        this.cancelOrderList = cancelOrderList;
    }

    /**
     * @description:(撤单订单)
     * <AUTHOR>
     * @date 2017年4月17日 上午10:31:29
     * @since JDK 1.6
     */
    public static class CancelOrderBean implements Serializable {

        /**
         * serialVersionUID:TODO（用一句话描述这个变量表示什么）
         *
         * @since Ver 1.1
         */

        private static final long serialVersionUID = 4722612590790038726L;
        /**
         * 客户订单号
         */
        private String dealNo;
        /**
         * 分销机构
         */
        private String disCode;

        /**
         * 好买香港代销标识: 0-否; 1-是
         */
        private String hkSaleFlag;

        /**
         * 产品购买状态,CAN_BUY:可购买,CAN_NOT_BUY:不可购买,CAN_MODIFY:可修改,CAN_NOT_MODIFY:不可修改;
         */
        private String fundBuyStatus;
        /**
         * 交易账号
         */
        private String txAcctNo;
        /**
         * 资金账号
         */
        private String cpAcctNo;
        /**
         * 银行账号
         */
        private String bankAcct;
        /**
         * 银行代码
         */
        private String bankCode;
        /**
         * 银行名称
         */
        private String bankName;
        /**
         * 产品名称
         */
        private String productName;
        /**
         * 产品代码
         */
        private String productCode;
        /**
         * 支付方式
         */
        private String paymentType;
        /**
         * 申请金额
         */
        private BigDecimal appAmt;
        /**
         * 申请份额
         */
        private BigDecimal appVol;
        /**
         * 申请比例
         */
        private BigDecimal appRatio;
        /**
         * 申请日期时间
         */
        private Date appDtm;
        /**
         * 付款状态
         */
        private String payStatus;
        /**
         * 订单状态
         */
        private String orderStatus;
        /**
         * TA交易日期
         */
        private String taTradeDt;
        /**
         * 手续费
         */
        private BigDecimal fee;
        /**
         * 中台业务码
         */
        private String mBusiCode;
        /**
         * 首次购买标识
         */
        private String firstBuyFlag;
        /**
         * 赎回去向
         */
        private String redeemDirection;
        /**
         * 合并上报标识 1-合并上报
         */
        private String mergeSubmitFlag;
        /**
         * 交易渠道 1-柜台；2-网站；3-电话；4-Wap；5-App
         */
        private String txChannel;

        public String getHkSaleFlag() {
            return hkSaleFlag;
        }

        public void setHkSaleFlag(String hkSaleFlag) {
            this.hkSaleFlag = hkSaleFlag;
        }

        public String getFundBuyStatus() {
            return fundBuyStatus;
        }

        public void setFundBuyStatus(String fundBuyStatus) {
            this.fundBuyStatus = fundBuyStatus;
        }

        public String getFirstBuyFlag() {
            return firstBuyFlag;
        }

        public void setFirstBuyFlag(String firstBuyFlag) {
            this.firstBuyFlag = firstBuyFlag;
        }

        public String getDealNo() {
            return dealNo;
        }

        public void setDealNo(String dealNo) {
            this.dealNo = dealNo;
        }

        public String getDisCode() {
            return disCode;
        }

        public void setDisCode(String disCode) {
            this.disCode = disCode;
        }

        public String getTxAcctNo() {
            return txAcctNo;
        }

        public void setTxAcctNo(String txAcctNo) {
            this.txAcctNo = txAcctNo;
        }

        public String getCpAcctNo() {
            return cpAcctNo;
        }

        public void setCpAcctNo(String cpAcctNo) {
            this.cpAcctNo = cpAcctNo;
        }

        public String getBankAcct() {
            return bankAcct;
        }

        public void setBankAcct(String bankAcct) {
            this.bankAcct = bankAcct;
        }

        public String getBankCode() {
            return bankCode;
        }

        public void setBankCode(String bankCode) {
            this.bankCode = bankCode;
        }

        public String getBankName() {
            return bankName;
        }

        public void setBankName(String bankName) {
            this.bankName = bankName;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public String getProductCode() {
            return productCode;
        }

        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }

        public String getPaymentType() {
            return paymentType;
        }

        public void setPaymentType(String paymentType) {
            this.paymentType = paymentType;
        }

        public BigDecimal getAppAmt() {
            return appAmt;
        }

        public void setAppAmt(BigDecimal appAmt) {
            this.appAmt = appAmt;
        }

        public BigDecimal getAppVol() {
            return appVol;
        }

        public void setAppVol(BigDecimal appVol) {
            this.appVol = appVol;
        }

        public BigDecimal getAppRatio() {
            return appRatio;
        }

        public void setAppRatio(BigDecimal appRatio) {
            this.appRatio = appRatio;
        }

        public Date getAppDtm() {
            return appDtm;
        }

        public void setAppDtm(Date appDtm) {
            this.appDtm = appDtm;
        }

        public String getPayStatus() {
            return payStatus;
        }

        public void setPayStatus(String payStatus) {
            this.payStatus = payStatus;
        }

        public String getOrderStatus() {
            return orderStatus;
        }

        public void setOrderStatus(String orderStatus) {
            this.orderStatus = orderStatus;
        }

        public String getTaTradeDt() {
            return taTradeDt;
        }

        public void setTaTradeDt(String taTradeDt) {
            this.taTradeDt = taTradeDt;
        }

        public BigDecimal getFee() {
            return fee;
        }

        public void setFee(BigDecimal fee) {
            this.fee = fee;
        }

        public String getmBusiCode() {
            return mBusiCode;
        }

        public void setmBusiCode(String mBusiCode) {
            this.mBusiCode = mBusiCode;
        }

        public String getRedeemDirection() {
            return redeemDirection;
        }

        public void setRedeemDirection(String redeemDirection) {
            this.redeemDirection = redeemDirection;
        }

        public String getMergeSubmitFlag() {
            return mergeSubmitFlag;
        }

        public void setMergeSubmitFlag(String mergeSubmitFlag) {
            this.mergeSubmitFlag = mergeSubmitFlag;
        }

        public String getTxChannel() {
            return txChannel;
        }

        public void setTxChannel(String txChannel) {
            this.txChannel = txChannel;
        }
    }

}
