package com.howbuy.tms.high.orders.facade.search.querycustfundpurchaseinfo;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import com.howbuy.tms.high.orders.facade.search.querycustfundpurchaseinfo.bean.FundPurchaseInfo;

import java.util.List;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 * @description: 查询客户产品购买信息响应对象
 * @author: hongdong.xie
 * @date: 2025-03-20 13:19:43
 * @since JDK 1.8
 */
public class QueryCustFundPurchaseInfoResponse extends OrderSearchBaseResponse {
    /**
     * 基金购买信息列表
     */
    private List<FundPurchaseInfo> fundPurchaseInfoList;

    public List<FundPurchaseInfo> getFundPurchaseInfoList() {
        return fundPurchaseInfoList;
    }

    public void setFundPurchaseInfoList(List<FundPurchaseInfo> fundPurchaseInfoList) {
        this.fundPurchaseInfoList = fundPurchaseInfoList;
    }
} 