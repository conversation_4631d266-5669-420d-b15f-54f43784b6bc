/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.common;


import com.howbuy.tms.common.client.BaseResponse;



/**
 * @apiDefine orderBaseResponse 共有响应参数(res)
 * 
 * @apiSuccess {String} returnCode 返回代码
 * @apiSuccess {String} description 描述
 * 
 */
public class OrderBaseResponse extends BaseResponse {

    private static final long serialVersionUID = 2366386555645672858L;


}
