/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.facade.search.querycustfunddiv;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;

import java.io.Serializable;
import java.util.List;

/**
 * @description:(查询客户持有基金分红方式 resposne)
 * <AUTHOR>
 * @date 2017年4月17日 下午1:25:24
 * @since JDK 1.6
 */
public class QueryCustFundDivResponse extends OrderSearchBaseResponse{

    private static final long serialVersionUID = 373674603564538630L;

    /**
     * 是否持有好臻产品 0:没有,1:有
     */
    private String hasHZProduct;
    /**
     * 是否持有好买香港产品  0:没有,1:有
     */
    private String hasHKProduct;

    private List<CustFundDivBean> custFundDivList;

    public List<CustFundDivBean> getCustFundDivList() {
        return custFundDivList;
    }

    public String getHasHZProduct() {
        return hasHZProduct;
    }

    public void setHasHZProduct(String hasHZProduct) {
        this.hasHZProduct = hasHZProduct;
    }

    public String getHasHKProduct() {
        return hasHKProduct;
    }

    public void setHasHKProduct(String hasHKProduct) {
        this.hasHKProduct = hasHKProduct;
    }

    public void setCustFundDivList(List<CustFundDivBean> custFundDivList) {
        this.custFundDivList = custFundDivList;
    }

    public static class CustFundDivBean implements Serializable {

        /**
         * serialVersionUID:TODO（用一句话描述这个变量表示什么）
         *
         * @since Ver 1.1
         */

        private static final long serialVersionUID = 7305373797073147657L;

        /** 基金简称 **/
        private String fundAttr;
        /**
         * 好买基金简称
         */
        private String fundAttrHb;

        /** 基金代码. **/
        private String fundCode;

        /** 分红方式. 0-红利再投，1-现金红利 **/
        private String divMode;

        /** 份额类型 **/
        private String shareClass;

        /** 是否准许修改分红方式 :0-不准许修改，1-准许修改  ,2-确认中**/
        private String allowModifyDivMode;

        /** 预计确认日期 **/
        private String confirmDt;

        /** 协议号 **/
        private String protocalNo;

        /** 资金账号 **/
        private String cpAcctNo;

        /** 分销机构 **/
        private String disCode;

        public String getFundAttr() {
            return fundAttr;
        }

        public void setFundAttr(String fundAttr) {
            this.fundAttr = fundAttr;
        }

        public String getFundAttrHb() {
            return fundAttrHb;
        }

        public void setFundAttrHb(String fundAttrHb) {
            this.fundAttrHb = fundAttrHb;
        }

        public String getFundCode() {
            return fundCode;
        }

        public void setFundCode(String fundCode) {
            this.fundCode = fundCode;
        }

        public String getDivMode() {
            return divMode;
        }

        public void setDivMode(String divMode) {
            this.divMode = divMode;
        }

        public String getShareClass() {
            return shareClass;
        }

        public void setShareClass(String shareClass) {
            this.shareClass = shareClass;
        }

        public String getAllowModifyDivMode() {
            return allowModifyDivMode;
        }

        public void setAllowModifyDivMode(String allowModifyDivMode) {
            this.allowModifyDivMode = allowModifyDivMode;
        }

        public String getConfirmDt() {
            return confirmDt;
        }

        public void setConfirmDt(String confirmDt) {
            this.confirmDt = confirmDt;
        }

        public String getProtocalNo() {
            return protocalNo;
        }

        public void setProtocalNo(String protocalNo) {
            this.protocalNo = protocalNo;
        }

        public String getCpAcctNo() {
            return cpAcctNo;
        }

        public void setCpAcctNo(String cpAcctNo) {
            this.cpAcctNo = cpAcctNo;
        }

        public String getDisCode() {
            return disCode;
        }

        public void setDisCode(String disCode) {
            this.disCode = disCode;
        }
    }
     
}

