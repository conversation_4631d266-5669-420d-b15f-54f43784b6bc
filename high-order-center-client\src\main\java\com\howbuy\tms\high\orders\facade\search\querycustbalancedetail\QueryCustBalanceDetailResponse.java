/**
 * Copyright (c) 2016, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querycustbalancedetail;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 
 * @description:查询用户持仓明细(银行卡维度或者基金维度)
 */
public class QueryCustBalanceDetailResponse extends OrderSearchBaseResponse {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    private static final long serialVersionUID = 4905508292367558304L;

    /**
     * 用户持仓信息
     */
    private List<CustBalanceDetail> custBalanceDetailList;


    public static class CustBalanceDetail implements Serializable {

        /**
         * serialVersionUID:TODO（用一句话描述这个变量表示什么）
         *
         * @since Ver 1.1
         */
        private static final long serialVersionUID = -1866379637372940457L;
        
        /**
         * 资金账号
         */
        private String cpAcctNo;
        /**
         * 基金代码
         */
        private String fundCode;
        /**
         * 份额类型
         */
        private String fundShareClass;
        /**
         * 基金简称
         */
        private String fundAttr;
        /**
         * 基金TA代码
         */
        private String taCode;
        /**
         * 协议类型
         */
        private String protocolType;
        /**
         * 协议号
         */
        private String protocolNo;
        /**
         * 总份额
         */
        private BigDecimal balanceVol;
        /**
         * 可用份额
         */
        private BigDecimal availVol;
        /**
         * 冻结份额
         */
        private BigDecimal unconfirmedVol;
        /**
         * 司法冻结份额
         */
        private BigDecimal justFrznVol;
        /**
         * 交易渠道
         */
        private String productChannel;

        /**
         * 分销机构号
         */
        private String disCode;

        public String getCpAcctNo() {
            return cpAcctNo;
        }

        public void setCpAcctNo(String cpAcctNo) {
            this.cpAcctNo = cpAcctNo;
        }

        public String getFundCode() {
            return fundCode;
        }

        public void setFundCode(String fundCode) {
            this.fundCode = fundCode;
        }

        public String getFundAttr() {
            return fundAttr;
        }

        public void setFundAttr(String fundAttr) {
            this.fundAttr = fundAttr;
        }
        
        public String getTaCode() {
            return taCode;
        }

        public void setTaCode(String taCode) {
            this.taCode = taCode;
        }

        public String getProtocolType() {
            return protocolType;
        }

        public void setProtocolType(String protocolType) {
            this.protocolType = protocolType;
        }

        public String getProtocolNo() {
            return protocolNo;
        }

        public void setProtocolNo(String protocolNo) {
            this.protocolNo = protocolNo;
        }


        public BigDecimal getBalanceVol() {
            return balanceVol;
        }

        public void setBalanceVol(BigDecimal balanceVol) {
            this.balanceVol = balanceVol;
        }

        public BigDecimal getAvailVol() {
            return availVol;
        }

        public void setAvailVol(BigDecimal availVol) {
            this.availVol = availVol;
        }

        public BigDecimal getUnconfirmedVol() {
            return unconfirmedVol;
        }

        public void setUnconfirmedVol(BigDecimal unconfirmedVol) {
            this.unconfirmedVol = unconfirmedVol;
        }

        public BigDecimal getJustFrznVol() {
            return justFrznVol;
        }

        public void setJustFrznVol(BigDecimal justFrznVol) {
            this.justFrznVol = justFrznVol;
        }

        public String getProductChannel() {
            return productChannel;
        }

        public void setProductChannel(String productChannel) {
            this.productChannel = productChannel;
        }

        public String getFundShareClass() {
            return fundShareClass;
        }

        public void setFundShareClass(String fundShareClass) {
            this.fundShareClass = fundShareClass;
        }

        public String getDisCode() {
            return disCode;
        }

        public void setDisCode(String disCode) {
            this.disCode = disCode;
        }
    }

    public List<CustBalanceDetail> getCustBalanceDetailList() {
        return custBalanceDetailList;
    }

    public void setCustBalanceDetailList(List<CustBalanceDetail> custBalanceDetailList) {
        this.custBalanceDetailList = custBalanceDetailList;
    }

}
