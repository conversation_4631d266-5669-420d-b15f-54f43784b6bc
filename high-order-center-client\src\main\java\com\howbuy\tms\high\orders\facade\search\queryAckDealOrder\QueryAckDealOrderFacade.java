package com.howbuy.tms.high.orders.facade.search.queryAckDealOrder;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * <AUTHOR>
 * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryAckDealOrder.QueryAckDealOrderFacade.execute(QueryAckDealOrderRequest request)
 * @apiVersion 1.0.0
 * @apiGroup QueryAckDealOrderFacade
 * @apiName execute
 * @apiDescription 查询用户确认订单
 * @apiParam (请求参数) {Array} fundCodeList 订单明细
 * @apiParam (请求参数) {String} submitOrAckStartDt 上报日期或者确认开始日期,yyyyMMdd
 * @apiParam (请求参数) {String} submitOrAckEndDt 上报日期或者确认结束日期,yyyyMMdd
 * @apiParam (请求参数) {String} txAcctNo 交易账号
 * @apiParam (请求参数) {String} hbOneNo 一账通账号
 * @apiParam (请求参数) {String} disCode 分销机构代码
 * @apiParam (请求参数) {String} outletCode 网点代码
 * @apiParam (请求参数) {String} appDt 申请日期
 * @apiParam (请求参数) {String} appTm 申请时间
 * @apiParam (请求参数) {Number} pageNo 页码
 * @apiParam (请求参数) {Number} pageSize 每页记录数
 * @apiParam (请求参数) {String} operIp 交易IP
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
 * @apiParam (请求参数) {String} dataTrack 数据跟踪
 * @apiParam (请求参数) {String} subOutletCode 子网点代码
 * @apiParamExample 请求参数示例
 * fundCodeList=oToUjov&hbOneNo=j0qZz&pageSize=4711&disCode=y0dOZj&txChannel=qFhrY&submitOrAckDt=pt2l&appTm=Aik&subOutletCode=zAghFy&pageNo=7050&operIp=hWFYIT6fX9&txAcctNo=wPaCU0ij1&appDt=QNNg0X70Sm&dataTrack=yu3&txCode=VfZxCCnl&outletCode=art
 * @apiSuccess (响应结果) {Array} fundAckDealDtoList 产品维度订单信息
 * @apiSuccess (响应结果) {String} fundAckDealDtoList.fundCode 产品编码
 * @apiSuccess (响应结果) {Array} fundAckDealDtoList.dealNoList 订单号列表
 * @apiSuccess (响应结果) {Number} fundAckDealDtoList.buyCount 购买订单数量
 * @apiSuccess (响应结果) {Number} fundAckDealDtoList.redeemCount 赎回订单数量
 * @apiSuccess (响应结果) {String} returnCode 返回码
 * @apiSuccess (响应结果) {String} description 描述
 * @apiSuccess (响应结果) {Number} totalCount 总记录数
 * @apiSuccess (响应结果) {Number} totalPage 总页数
 * @apiSuccess (响应结果) {Number} pageNo 当前页
 * @apiSuccess (响应结果) {String} txId txId
 * @apiSuccessExample 响应结果示例
 * {"returnCode":"ws8h8MI","totalPage":1539,"pageNo":7372,"description":"F","txId":"KMk0G","fundAckDealDtoList":[{"fundCode":"c","dealNoList":["Z"]}],"totalCount":5119}
 */
public interface QueryAckDealOrderFacade extends BaseFacade<QueryAckDealOrderRequest, QueryAckDealOrderResponse> {
}
