/**
 * Copyright (c) 2022, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * @description: (查询管理人下可购买的产品列表)
 * <AUTHOR>
 * @date 2022/5/5 14:57
 * @since JDK 1.8
 */
public interface QueryCanBuyFundsForDsFacade extends BaseFacade<QueryCanBuyFundsForDsRequest, QueryCanBuyFundsForDsResponse> {

}