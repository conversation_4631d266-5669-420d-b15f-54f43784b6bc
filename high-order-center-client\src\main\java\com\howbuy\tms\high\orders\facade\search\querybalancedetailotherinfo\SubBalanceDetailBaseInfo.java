package com.howbuy.tms.high.orders.facade.search.querybalancedetailotherinfo;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:持仓基础信息
 * @Author: yun.lu
 * Date: 2023/9/1 14:15
 */
@Data
public class SubBalanceDetailBaseInfo extends BaseDto {
    /**
     * 产品编码
     */
    private String fundCode;

    /**
     * 一账通号
     */
    private String honeNo;

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 确认日期
     */
    private String ackDt;

    /**
     * 持仓份额
     */
    private BigDecimal balanceVol;

    /**
     * 赎回开放日期
     */
    private String openRedeemDt;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 银行卡号
     */
    private String bankAcct;

    /**
     * 银行卡号掩码
     */
    private String bankAcctMask;

    /**
     * 银行名
     */
    private String bankName;

    /**
     *  银行编号
     */
    private String bankCode;

    /**
     * 可用份额
     */
    private BigDecimal availVol;
}
