package com.howbuy.tms.high.orders.facade.search.querybalancedetailotherinfo;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:查询持仓详情补充信息接口
 * @Author: yun.lu
 * Date: 2023/8/30 13:26
 */
@Getter
@Setter
public class QueryBalanceDetailOtherInfoResponse extends OrderSearchBaseResponse {
    /**
     * 产品编码
     */
    private String fundCode;
    /**
     * 一账通号
     */
    private String honeNo;
    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 累计收益(当前币种）
     */
    private BigDecimal accumIncome;
    /**
     * 日收益（当前币种）
     */
    private BigDecimal dailyAssetCurrency;
    /**
     * 最新收益率
     */
    private BigDecimal dayAssetRate;
    /**
     * 最新收益日期
     */
    private String incomeLatestDay;

    /**
     * 连续持有天数
     */
    private Long balanceDayNum;

    /**
     * 开始持有日期
     */
    private String startHoldDay;

    /**
     * 当前日期
     */
    private String nowDt;

    /**
     * 该产品在“特殊隐藏配置表”,1:有;0:没有
     */
    private String inHiddenConf;

    /**
     * 该产品在“特殊产品交易类型转译表”内 ,1:有;0:没有
     */
    private String inSpecialTransfer;


    private List<SubBalanceDetailBaseInfo> subBalanceDetailBaseInfoList;

}
