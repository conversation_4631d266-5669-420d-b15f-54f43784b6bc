---
description: 创建查询接口定义和实现规则
globs: 
alwaysApply: false
---
# 高端查询类Dubbo接口生成规则
#==================== 查询类Dubbo接口生成规范 ====================
# 查询类Dubbo接口包名与命名规范
query_interface_rules:
  - 接口主包：com.howbuy.tms.high.orders.facade
  - 查询子包：com.howbuy.tms.high.orders.facade.search.{具体业务功能}
  - 接口命名：业务功能+Facade (如QueryBuyFundStatusFacade)
  - 接口继承：BaseFacade<请求类型, 响应类型>
  - 请求类型：业务功能+Request
  - 响应类型：业务功能+Response

# 查询类接口实现类规范
query_impl_rules:
  - 实现类包名规则：
    - 主包：com.howbuy.tms.high.orders.service.facade
    - 子包结构：应与接口包结构对应，如search.{具体业务功能}
  - 实现类命名：接口名+Service (如QueryBuyFundStatusFacadeService)
  - 必须实现相应接口：implements XXXFacade
  - 注解使用：
    - @Slf4j - 用于日志记录
    - @DubboService - 标识为Dubbo服务
    - @Component - 注册为Spring组件

# 查询接口参数规范
query_request_rules:
  - 请求对象包名：com.howbuy.tms.high.orders.facade.search.{具体业务功能}
  - 类命名：业务功能+Request
  - 通用参数：
    - 分页参数：pageNo, pageSize
    - 交易渠道参数：txChannel (1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构等)
    - 交易账号：txAcctNo
    - 一账通号：hbOneNo
    - 分销机构号：disCode, 
    - outletCode：网点号
    - subOutletCode：子网点号
    - 交易日期时间：appDt, appTm
    - 交易IP：operIp
    - 交易码：txCode
  - 参数验证：使用@MyValidation(validatorType = ValidatorTypeEnum.类型, fieldName = "中文名", isRequired = true)
  - 数组/列表类型：使用List<T>类型，集合中的元素类型根据业务需要确定

# 查询接口响应规范
query_response_rules:
  - 响应对象包名：com.howbuy.tms.high.orders.facade.search.{具体业务功能}
  - 类命名：业务功能+Response
  - 通用响应字段：
    - 返回码：returnCode
    - 描述信息：description
    - 交易账号：txAcctNo
  - 分页查询通用字段：
    - 总记录数：totalCount
    - 总页数：totalPage
    - 当前页：pageNo
  - 业务数据字段：根据业务需求定义，通常使用List<具体业务对象>

#==================== 查询类接口APIDOC注释规范 ====================

# 查询类接口APIDOC规范
query_apidoc_rules:
  - 接口描述格式：
    ```
    * @api {dubbo} com.howbuy.tms.high.orders.facade.search.具体业务功能.接口名称.execute()
    * @apiVersion 版本号
    * @apiGroup 实现类名
    * @apiName execute
    * @apiDescription 接口功能描述
    ```
  - 请求参数格式：
    ```
    * @apiParam (请求参数) {类型} 参数名 参数说明
    ```
  - 响应结果格式：
    ```
    * @apiSuccess (响应结果) {类型} 字段名 字段说明
    ```
  - 列表类型注释：
    ```
    * @apiSuccess (响应结果) {Array} 列表名 列表说明
    * @apiSuccess (响应结果) {类型} 列表名.字段名 字段说明
    ```
  - 示例格式：
    ```
    * @apiParamExample 请求参数示例
    * 参数示例
    * @apiSuccessExample 响应结果示例
    * 响应示例
    ```

# 查询接口实现类注释规范
query_impl_comment_rules:
  ```
  /**
   * 
   * @description:(接口功能描述)
   * @reason:
   * <AUTHOR>
   * @date 日期
   * @since JDK 1.8
   */
  ```

#==================== 代码示例 ====================

# 查询类Dubbo接口示例
query_interface_example:
  ```java
  /**
   * @api {dubbo} com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusFacade.execute()
   * @apiVersion 1.0.0
   * @apiGroup QueryBuyFundStatusFacadeService
   * @apiName execute
   * @apiDescription 查询产品购买状态
   * @apiParam (请求参数) {Array} productCodeList 产品代码
   * @apiParam (请求参数) {String} appointmentFlag 预约日历场景标识 1-是
   * @apiParam (请求参数) {String} homePage 是否首页使用 1-是
   * @apiParam (请求参数) {String} notCheckChannel 是否不校验渠道
   * @apiParam (请求参数) {String} txAcctNo 交易账号
   * @apiParam (请求参数) {String} hbOneNo 一账通账号
   * @apiParam (请求参数) {String} disCode 分销机构代码
   * @apiParam (请求参数) {String} outletCode 网点代码
   * @apiParam (请求参数) {String} appDt 申请日期
   * @apiParam (请求参数) {String} appTm 申请时间
   * @apiParam (请求参数) {Number} pageNo 页码
   * @apiParam (请求参数) {Number} pageSize 每页记录数
   * @apiParam (请求参数) {String} operIp 交易IP
   * @apiParam (请求参数) {String} txCode 交易码
   * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
   * @apiParam (请求参数) {String} dataTrack 数据跟踪
   * @apiParam (请求参数) {String} subOutletCode 子网点代码
   * @apiParamExample 请求参数示例
   * hbOneNo=x9OxzL&notCheckChannel=HDsy&productCodeList=ebB0k&pageSize=4827&disCode=rZVR&txChannel=T68&homePage=O&appTm=7A&subOutletCode=0&pageNo=6287&operIp=Nh4&txAcctNo=XSY&appointmentFlag=n2eMQZJ4Au&appDt=apHEu&dataTrack=lHZacuU&txCode=7bl&outletCode=hfOp
   * @apiSuccess (响应结果) {String} txAcctNo 交易账号
   * @apiSuccess (响应结果) {Array} buyFundStatusList 产品状态list
   * @apiSuccess (响应结果) {String} buyFundStatusList.productCode 产品代码
   * @apiSuccess (响应结果) {String} buyFundStatusList.shareClass 收费类型A-前收费;B-后收费
   * @apiSuccess (响应结果) {String} buyFundStatusList.buyStatus 产品状态0-不可购买 1-可购买
   * @apiSuccess (响应结果) {String} buyFundStatusList.fundBuyStatus 产品购买状态,CAN_BUY:可购买,CAN_NOT_BUY:不可购买,CAN_MODIFY:可修改,CAN_NOT_MODIFY:不可修改;
   * @apiSuccess (响应结果) {String} buyFundStatusList.buyStatusType 状态标识1-正常 2-代销不支持 3.年龄限制 4-已售罄 5-直销转代销的黑名单6-产品状态不可购买或不在预约期内 8-产品参数配置有误99-其它
   * @apiSuccess (响应结果) {String} buyFundStatusList.msg 说明信息
   * @apiSuccess (响应结果) {String} returnCode 返回码
   * @apiSuccess (响应结果) {String} description 描述
   * @apiSuccess (响应结果) {Number} totalCount 总记录数
   * @apiSuccess (响应结果) {Number} totalPage 总页数
   * @apiSuccess (响应结果) {Number} pageNo 当前页
   * @apiSuccessExample 响应结果示例
   * {"returnCode":"5JybBVc","buyFundStatusList":[{"msg":"GozNGN","productCode":"OSXGhFQ8y","shareClass":"a8k5cyPj4","buyStatus":"UJq8","buyStatusType":"oxYjRF","fundBuyStatus":"TG2"}],"totalPage":8933,"pageNo":4438,"txAcctNo":"0","description":"CM2","totalCount":4609}
   */
  /**
   * 
   * @description:(查询产品购买状态)
   * @reason:
   * <AUTHOR>
   * @date 2017年11月28日 上午9:14:32
   * @since JDK 1.6
   */
  public interface QueryBuyFundStatusFacade extends BaseFacade<QueryBuyFundStatusRequest, QueryBuyFundStatusResponse> {
  
  }
  ```

# 查询请求对象示例
query_request_example:
  ```java
  /**
   * 
   * @description:(查询产品购买状态请求)
   * @reason:
   * <AUTHOR>
   * @date 2017年11月28日 上午9:14:32
   * @since JDK 1.6
   */
  @Setter
  @Getter
  @EqualsAndHashCode(callSuper = true)
  public class QueryBuyFundStatusRequest extends BaseRequest {
  
      private static final long serialVersionUID = 1L;
      
      /**
       * 产品代码列表
       */
      private List<String> productCodeList;
      
      /**
       * 预约日历场景标识
       * 1-是
       */
      private String appointmentFlag;
      
      /**
       * 是否首页使用
       * 1-是
       */
      private String homePage;
      
      /**
       * 是否不校验渠道
       */
      private String notCheckChannel;
      
      /**
       * 交易账号
       */
      private String txAcctNo;
      
      /**
       * 一账通账号
       */
      private String hbOneNo;
      
      /**
       * 分销机构代码
       */
      private String disCode;
      
      /**
       * 网点代码
       */
      private String outletCode;
      
      /**
       * 申请日期
       */
      private String appDt;
      
      /**
       * 申请时间
       */
      private String appTm;
      
      /**
       * 页码
       */
      private Integer pageNo;
      
      /**
       * 每页记录数
       */
      private Integer pageSize;
      
      /**
       * 交易IP
       */
      private String operIp;
      
      /**
       * 交易码
       */
      private String txCode;
      
      /**
       * 交易渠道
       * 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
       */
      private String txChannel;
      
      /**
       * 数据跟踪
       */
      private String dataTrack;
      
      /**
       * 子网点代码
       */
      private String subOutletCode;
  }
  ```

# 查询响应对象示例
query_response_example:
  ```java
  /**
   * 
   * @description:(查询产品购买状态响应)
   * @reason:
   * <AUTHOR>
   * @date 2017年11月28日 上午9:14:32
   * @since JDK 1.6
   */
  @Setter
  @Getter
  public class QueryBuyFundStatusResponse implements Serializable {
  
      private static final long serialVersionUID = 1L;
      
      /**
       * 交易账号
       */
      private String txAcctNo;
      
      /**
       * 产品状态list
       */
      private List<BuyFundStatus> buyFundStatusList;
      
      /**
       * 返回码
       */
      private String returnCode;
      
      /**
       * 描述
       */
      private String description;
      
      /**
       * 总记录数
       */
      private Integer totalCount;
      
      /**
       * 总页数
       */
      private Integer totalPage;
      
      /**
       * 当前页
       */
      private Integer pageNo;
      
      /**
       * 产品购买状态
       */
      @Setter
      @Getter
      public static class BuyFundStatus implements Serializable {
          
          private static final long serialVersionUID = 1L;
          
          /**
           * 产品代码
           */
          private String productCode;
          
          /**
           * 收费类型
           * A-前收费;B-后收费
           */
          private String shareClass;
          
          /**
           * 产品状态
           * 0-不可购买 1-可购买
           */
          private String buyStatus;
          
          /**
           * 产品购买状态
           * CAN_BUY:可购买,CAN_NOT_BUY:不可购买,CAN_MODIFY:可修改,CAN_NOT_MODIFY:不可修改;
           */
          private String fundBuyStatus;
          
          /**
           * 状态标识
           * 1-正常 2-代销不支持 3.年龄限制 4-已售罄 5-直销转代销的黑名单6-产品状态不可购买或不在预约期内 8-产品参数配置有误99-其它
           */
          private String buyStatusType;
          
          /**
           * 说明信息
           */
          private String msg;
      }
  }
  ```

# 查询接口实现类示例
query_impl_example:
  ```java
  /**
   * 
   * @description:(查询产品购买状态)
   * @reason:
   * <AUTHOR>
   * @date 2017年11月28日 上午9:14:32
   * @since JDK 1.6
   */
  @Slf4j
  @DubboService
  @Component
  public class QueryBuyFundStatusFacadeService implements QueryBuyFundStatusFacade {
  
      @Resource
      private QueryBuyFundStatusService queryBuyFundStatusService;
      
      @Override
      public Response<QueryBuyFundStatusResponse> execute(QueryBuyFundStatusRequest request) {
          log.info("查询产品购买状态，请求参数：{}", JsonUtils.toJsonString(request));
          return queryBuyFundStatusService.execute(request);
      }
  }
  ```

# 生成指南
generate_guide:
  1. 明确查询业务需求，确定接口名称和功能
  2. 在正确的包路径下创建接口和对应的请求/响应类
  3. 同步在service模块中创建对应的实现类
  4. 按规范编写APIDOC注释，确保包含所有必要标签
  5. 请求类中包含通用参数和业务特定参数
  6. 响应类中包含通用响应字段和业务数据字段
  7. 对于列表类型的响应数据，创建内部静态类
  8. 所有字段添加明确的中文注释，说明用途和格式要求
  9. 提供真实的请求和响应示例
  10. 对枚举值和特殊格式字段提供详细说明 