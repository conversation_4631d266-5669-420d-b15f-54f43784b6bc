package com.howbuy.tms.high.orders.facade.search.queryasset;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:资产中心收益信息实体
 * @Author: yun.lu
 * Date: 2023/8/21 20:14
 */
@Data
public class StagesIncomeDto extends BaseDto {
    /**
     * 累计收益
     */
    private BigDecimal accumIncome;
    /**
     * 最新收益
     */
    private BigDecimal dayIncome;
    /**
     * 最新收益率
     */
    private BigDecimal dayIncomeRate;
    /**
     * 最新收益日期
     */
    private String currentDay;

    /**
     * 连续持有天数
     */
    private Long contHoldDays;

    /**
     * 开始持有日期
     */
    private String startHoldDay;

    /**
     * 当前日期
     */
    private String nowDt;
}
