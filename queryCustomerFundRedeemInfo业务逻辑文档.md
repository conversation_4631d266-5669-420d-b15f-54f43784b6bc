# queryCustomerFundRedeemInfo方法业务逻辑文档

## 文档信息
- **方法名称**: queryCustomerFundRedeemInfo
- **所属类**: QueryCustomerRedeemAppointInfoLogicService
- **作者**: shaoyang.li
- **创建时间**: 2025-08-07 13:54:12
- **文档版本**: v1.0

## 方法概述
该方法用于查询用户产品的可赎回信息，是赎回业务的核心逻辑。通过多层校验和计算，确定用户是否可以赎回指定产品，以及可赎回的份额数量。

## 输入参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| txAcctNo | String | 是 | 交易账号 |
| fundCode | String | 是 | 产品编码 |

## 返回值
- **类型**: CustomerFundRedeemInfo
- **说明**: 用户产品赎回信息对象，包含可赎回状态、份额信息等

## 主要业务流程

### 1. 初始化阶段
- 创建返回对象CustomerFundRedeemInfo
- 设置基本信息（产品编码、交易账号）
- 默认设置为可赎回状态（ALLOW）和正常类型（NORMAL）

### 2. 产品基础信息校验
- 调用外部服务查询产品基础信息
- **校验规则**: 如果查询不到产品基础信息，则设置为不可赎回
- **失败处理**: 设置状态为NOT_ALLOW，类型为ERROR_CONFIG

### 3. 赎回交易开通状态校验
- **适用范围**: 仅对TP_SM和HIGH_FUND渠道的产品进行校验
- **校验内容**: 检查产品是否开通赎回交易
- **校验规则**: 如果未开通或状态为关闭，则不允许赎回

### 4. 产品控制信息校验
- 查询产品控制信息（HighProductControlModel）
- **校验规则**: 产品控制信息必须存在，否则不允许赎回

### 5. 在途赎回份额查询
- 查询用户未确认的赎回份额
- 按资金账号（cpAcctNo）进行分组汇总
- 用于后续可赎回份额计算

### 6. 锁定期处理分支
根据产品是否有份额锁定期（hasLockPeriod），分为两个处理分支：

#### 6.1 有锁定期处理（hasLockPeriodFundRedeemInfo）
**核心逻辑**：
1. **获取份额锁定信息**
   - 查询用户的子账本份额明细
   - 获取每个份额对应的赎回预约日历信息

2. **预约日历校验**
   - 如果查询不到可赎回日历，设置为不可赎回
   - 根据开放赎回日期查询对应的预约信息

3. **锁定状态计算**
   - 支持预约的产品：根据预约开始/结束时间和开放时间计算
   - 不支持预约的产品：根据锁定期和当前时间计算
   - 循环锁定产品：特殊处理锁定期更新逻辑

4. **份额计算**
   - 按资金账号分组处理
   - 每个资金账号计算：可赎回份额 = 子份额可赎回总额 - 在途赎回份额
   - 汇总所有资金账号的份额信息

5. **产品状态校验**
   - 对每个份额明细，校验对应赎回日期的产品基金状态
   - 如果状态不允许赎回，将该份额的可赎回数量设为0

#### 6.2 无锁定期处理（noLockPeriodFundRedeemInfo）
**核心逻辑**：
1. **预约赎回支持检查**
   - 如果支持预约赎回，需要查询预约开放日历
   - 校验当前时间是否在预约时间范围内

2. **产品状态校验**
   - 查询产品在指定日期的基金状态
   - 校验状态是否允许赎回

3. **份额查询**
   - 查询资金维度的确认份额明细
   - 计算可赎回份额：可用份额 - 冻结份额 - 在途份额

## 锁定状态枚举说明
| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| 1 | LOCKING | 锁定中，不可赎回 |
| 2 | LOCK_REDEEMABLE | 锁定可赎回，可以赎回 |
| 3 | LOCK_OVER | 锁定已过，不可赎回 |
| 4 | CAN_NOT_REDEEM | 不可赎回 |

## 赎回状态枚举说明
| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| 1 | ALLOW | 可赎回 |
| 0 | NOT_ALLOW | 不可赎回 |

## 状态类型枚举说明
| 类型码 | 类型名称 | 说明 |
|--------|----------|------|
| 1 | NORMAL | 正常状态 |
| 99 | ERROR_CONFIG | 配置错误 |

## 关键业务规则

### 1. 份额计算规则
- **可赎回份额** = 可用份额 - 锁定份额 - 在途赎回份额
- **总份额** = 所有子账本份额之和
- **锁定份额** = 处于锁定状态的份额总和

### 2. 时间判断规则
- **锁定中**: 当前时间 < 可赎回开始时间
- **锁定可赎回**: 可赎回开始时间 ≤ 当前时间 ≤ 可赎回结束时间
- **锁定已过**: 当前时间 > 可赎回结束时间

### 3. 预约赎回规则
- 支持预约的产品需要在预约时间范围内才能赎回
- 预约结束时间与开放时间的关系影响可赎回时间计算
- 循环锁定产品有特殊的锁定期更新机制

### 4. 产品状态校验规则
- 产品基金状态必须在可赎回状态集合中
- 不同赎回日期可能对应不同的产品状态
- 状态不符合要求的份额不可赎回

## 异常处理机制

### 1. 配置异常
- 产品基础信息不存在
- 产品控制信息缺失
- 交易未开通
- 预约日历配置错误

### 2. 业务异常
- 份额不足
- 不在预约时间范围内
- 产品状态不允许赎回
- 锁定期配置异常

### 3. 数据异常
- 循环锁定产品锁定期未及时更新
- 可赎回结束时间小于开始时间
- 预约时间配置不合理

## 性能考虑
1. **批量查询**: 对外部服务调用进行批量处理
2. **缓存机制**: 工作日信息等可缓存数据
3. **并发处理**: 支持多线程处理多个产品
4. **数据分组**: 按资金账号分组减少重复计算

## 业务流程图

```mermaid
flowchart TD
    A[开始: queryCustomerFundRedeemInfo] --> B[初始化CustomerFundRedeemInfo对象]
    B --> C[查询产品基础信息]
    C --> D{产品基础信息存在?}
    D -->|否| E[设置不可赎回状态<br/>ERROR_CONFIG]
    D -->|是| F{是否为TP_SM或HIGH_FUND渠道?}
    F -->|是| G[查询赎回交易开通配置]
    F -->|否| I[查询产品控制信息]
    G --> H{赎回交易已开通?}
    H -->|否| E
    H -->|是| I
    I --> J{产品控制信息存在?}
    J -->|否| E
    J -->|是| K[查询在途赎回份额]
    K --> L{产品是否有锁定期?}
    L -->|是| M[有锁定期处理逻辑]
    L -->|否| N[无锁定期处理逻辑]

    M --> M1[查询用户份额明细]
    M1 --> M2{份额明细存在?}
    M2 -->|否| E
    M2 -->|是| M3[查询预约日历信息]
    M3 --> M4[按资金账号分组处理]
    M4 --> M5[计算每个份额的锁定状态]
    M5 --> M6[校验产品基金状态]
    M6 --> M7[计算可赎回份额]
    M7 --> M8{可赎回份额>0?}
    M8 -->|否| E
    M8 -->|是| P[设置可赎回状态]

    N --> N1{支持预约赎回?}
    N1 -->|是| N2[查询预约开放日历]
    N1 -->|否| N5[校验产品基金状态]
    N2 --> N3{在预约时间范围内?}
    N3 -->|否| E
    N3 -->|是| N5
    N5 --> N6{产品状态允许赎回?}
    N6 -->|否| E
    N6 -->|是| N7[查询资金维度份额明细]
    N7 --> N8[计算可赎回份额]
    N8 --> N9{可赎回份额>0?}
    N9 -->|否| E
    N9 -->|是| P

    E --> Q[返回不可赎回结果]
    P --> R[返回可赎回结果]
    Q --> S[结束]
    R --> S
```

## 有锁定期处理详细流程图

```mermaid
flowchart TD
    A[有锁定期处理开始] --> B[查询用户子账本份额明细]
    B --> C{份额明细存在?}
    C -->|否| D[设置不可赎回<br/>查询不到可赎回日历]
    C -->|是| E[提取开放赎回日期列表]
    E --> F[查询预约日历信息]
    F --> G[按资金账号分组]
    G --> H[遍历每个资金账号]

    H --> I[遍历该账号下的份额明细]
    I --> J{支持预约赎回?}
    J -->|是| K[预约赎回逻辑处理]
    J -->|否| L[非预约赎回逻辑处理]

    K --> K1{预约日历存在?}
    K1 -->|否| K2[设置锁定状态为LOCKING]
    K1 -->|是| K3[获取预约和开放时间]
    K3 --> K4{预约结束时间<开放开始时间?}
    K4 -->|是| K5[检查锁定期是否在开放区间]
    K4 -->|否| K6[复杂时间逻辑判断]
    K5 --> K7[设置可赎回时间范围]
    K6 --> K7
    K7 --> M[设置锁定状态]

    L --> L1{是否循环锁定?}
    L1 -->|是| L2[可赎回时间=锁定期当天]
    L1 -->|否| L3[可赎回时间=锁定期之后]
    L2 --> M
    L3 --> M

    M --> N[根据当前时间判断锁定状态]
    N --> O{锁定状态=LOCK_REDEEMABLE?}
    O -->|是| P[可赎回份额=可用份额<br/>锁定份额=0]
    O -->|否| Q[可赎回份额=0<br/>锁定份额=可用份额]
    P --> R[校验产品基金状态]
    Q --> R
    R --> S{产品状态允许赎回?}
    S -->|否| T[该份额可赎回数量=0]
    S -->|是| U[保持计算结果]
    T --> V[累加到资金账号总计]
    U --> V
    V --> W{还有其他份额?}
    W -->|是| I
    W -->|否| X[计算该账号最终可赎回份额]
    X --> Y[扣除在途赎回份额]
    Y --> Z{还有其他资金账号?}
    Z -->|是| H
    Z -->|否| AA[汇总所有账号结果]
    AA --> BB{总可赎回份额>0?}
    BB -->|是| CC[设置可赎回状态]
    BB -->|否| DD[设置不可赎回状态]
    CC --> EE[返回结果]
    DD --> EE
    D --> EE
```

## 核心算法说明

### 1. 锁定状态判断算法
```java
// 伪代码
if (当前时间 < 可赎回开始时间) {
    锁定状态 = LOCKING; // 锁定中
} else if (可赎回结束时间为空 || 当前时间 > 可赎回结束时间) {
    锁定状态 = LOCK_OVER; // 锁定已过
} else {
    锁定状态 = LOCK_REDEEMABLE; // 锁定可赎回
}
```

### 2. 可赎回份额计算算法
```java
// 有锁定期产品
for (每个资金账号) {
    资金账号可赎回份额 = 0;
    for (每个份额明细) {
        if (锁定状态 == LOCK_REDEEMABLE && 产品状态允许赎回) {
            份额可赎回数量 = 份额可用数量;
        } else {
            份额可赎回数量 = 0;
        }
        资金账号可赎回份额 += 份额可赎回数量;
    }
    资金账号最终可赎回份额 = 资金账号可赎回份额 - 该账号在途赎回份额;
}
总可赎回份额 = sum(所有资金账号最终可赎回份额);
```

### 3. 预约时间计算算法
根据预约结束时间与开放开始时间的关系，采用不同的计算逻辑：
- **情况1**: 预约结束时间 < 开放开始时间
  - 可赎回时间 = [预约开始时间, 预约结束时间]
- **情况2**: 预约结束时间 ≥ 开放开始时间
  - 根据锁定期与开放时间的关系进一步判断
  - 循环锁定产品特殊处理

## 重要提醒事项

### 1. 数据一致性
- 子账本份额明细与预约日历信息必须保持一致
- 在途赎回份额需要实时更新
- 产品基金状态可能随时变化

### 2. 时间处理
- 所有时间比较使用字符串格式（YYYYMMDD）
- 需要考虑工作日和节假日的影响
- 时区问题需要统一处理

### 3. 异常监控
- 循环锁定产品的锁定期更新异常
- 预约日历配置不合理的情况
- 产品状态异常变化

### 4. 性能优化
- 批量查询减少数据库访问
- 缓存常用的配置信息
- 异步处理非关键路径

## 扩展性设计
1. **策略模式**: 不同产品类型可扩展不同的计算策略
2. **配置驱动**: 业务规则通过配置控制
3. **插件机制**: 支持自定义校验和计算逻辑
4. **监控告警**: 异常情况自动告警机制

## 相关接口和服务

### 外部服务依赖
- **QueryHighProductOuterService**: 查询产品基础信息和状态
- **TradeDayService**: 获取工作日信息
- **ProductAppointmentInfoService**: 查询预约日历信息
- **HighProductService**: 查询产品控制信息

### 数据库访问
- **CustBooksDtlRepository**: 客户账本明细表
- **SubCustBooksService**: 子账本服务
- **CustBooksRepository**: 客户账本表

### 关键配置
- **MDataDic.CAN_REDEEM_SET**: 可赎回的产品状态集合
- **ProductChannelEnum**: 产品渠道枚举
- **BusinessCodeEnum**: 业务代码枚举

## 测试建议

### 1. 单元测试覆盖
- 各种异常情况的处理
- 边界值测试（份额为0、时间边界等）
- 不同产品类型的处理逻辑

### 2. 集成测试重点
- 外部服务调用的异常处理
- 数据库事务的一致性
- 并发访问的安全性

### 3. 性能测试
- 大量份额明细的处理性能
- 高并发场景下的响应时间
- 内存使用情况监控
