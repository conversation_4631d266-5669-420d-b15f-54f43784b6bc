/**
 * Copyright (c) 2022, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords;

import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2022/5/5 15:11
 * @since JDK 1.8
 */
public class QueryCanBuyFundsForDsRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = 4423587929886109761L;
    /**
     * @api {dubbo} com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords.QueryCanBuyFundsForDsFacade.execute() 查询管理人下可购买的产品列表
     * @apiGroup high-order-center
     * @apiDescription 查询管理人下可购买的产品列表
     *
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     *
     * @apiParam {List} [fundManCodeList] 产品管理人代码
     *
     * @apiUse orderBaseResponse
     * @apiUse orderSearchBaseResponse
     *
     * @apiSuccess (canBuyFundsList) {List} canBuyFundsList 可以购买产品列表
     * @apiSuccess (canBuyFundsList) {String} fundManCode 管理人代码
     * @apiSuccess (canBuyFundsList) {List} fundCodes 产品列表
     * @apiSuccess (canBuyFundsList) {long} totalCount 总记录数
     * @apiSuccess (canBuyFundsList) {long} totalPage 总页数
     * @apiSuccess (canBuyFundsList) {long} pageNo 当前页
     *
     * @apiSuccessExample {json} Response Example dubbo
     *                    com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords.QueryCanBuyFundsForDsResponse
     *
     */
    public QueryCanBuyFundsForDsRequest() {
        this.setTxCode(TxCodes.QUERY_BUY_FUND_STATUS_FOR_DS);
    }
    /**
     * 产品管理人代码
     */
    private List<String> fundManCodeList;

    public List<String> getFundManCodeList() {
        return fundManCodeList;
    }

    public void setFundManCodeList(List<String> fundManCodeList) {
        this.fundManCodeList = fundManCodeList;
    }
}