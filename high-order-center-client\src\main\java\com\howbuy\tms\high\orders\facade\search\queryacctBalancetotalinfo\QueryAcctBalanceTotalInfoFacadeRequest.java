package com.howbuy.tms.high.orders.facade.search.queryacctBalancetotalinfo;

import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Data;

import java.util.List;

/**
 * @Description:查询用户账户信息请求入参
 * @Author: yun.lu
 * Date: 2023/8/17 16:23
 */
@Data
public class QueryAcctBalanceTotalInfoFacadeRequest extends OrderSearchBaseRequest {
    /**
     * 配置产品的基金代码集合
     */
    private List<String> configFundCodeList;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryacctBalancetotalinfo.QueryAcctBalanceTotalInfoFacade.execute(QueryAcctBalanceTotalInfoFacadeRequest request)
     * @apiName 查询用户账户信息接口
     * @apiParam (request) {com.howbuy.tms.high.orders.facade.search.queryacctBalancetotalinfo.QueryAcctBalanceTotalInfoFacadeRequest} request 请求参数
     * @apiParam (request) {String} request.txAcctNo 交易账号
     * @apiParam (request) {String} request.hbOneNo 一账通账号
     * @apiParam (request) {Array} configFundCodeList 配置产品的基金代码集合
     * @apiSuccess (response) {com.howbuy.tms.high.orders.facade.search.queryacctBalancetotalinfo.QueryAcctBalanceTotalInfoFacadeResponse} response 返回参数
     * @apiSuccess (response) {String} response.hasBalance 当前账户无持仓,1:有;0:没有
     * @apiSuccess (response) {String} response.balanceOneYear 当前账户近1年内，有没有持仓,1:有;0:没有
     * @apiSuccess (response) {String} response.balanceHiddenConfFundOneYear 当前账户近1年内，曾经持有过“特殊隐藏配置表”内的产品,1:有;0:没有
     * @apiSuccess (response) {Array} response.balanceConfigFundCodeList 当前账户近1年内，曾经持有过的配置的持仓产品代码
     * @apiSuccess (response) {String} response.balanceTransferConfFundOneYear 当前账户近1年内，曾经持有过“特殊产品交易类型转译表”内的产品,1:有;0:没有
     * @apiSuccess (response) {String} response.hasHkProduct 当前账号是否有香港产品持仓,1:有;0:没有
     * @apiSuccess (response) {String} response.hasHzProduct 当前账号是否有好臻产品持仓,1:有;0:没有
     * @apiSuccessExample Success-Response:
     *  HTTP/1.1 200 OK
     *  {
     *    "hasBalance": "1",
     *    "balanceOneYear": "0",
     *    "balanceHiddenConfFundOneYear": "1",
     *    "balanceConfigFundCodeList": ["001","002","003"],
     *    "balanceTransferConfFundOneYear": "0",
     *    "hasHkProduct": "0",
     *    "hasHzProduct": "1"
     *  }
     */
    public QueryAcctBalanceTotalInfoFacadeRequest() {
        this.setTxCode(TxCodes.QUERY_ACCT_BALANCE_BASE_INFO);
        this.setDisCode(DisCodeEnum.HM.getCode());
    }

}
