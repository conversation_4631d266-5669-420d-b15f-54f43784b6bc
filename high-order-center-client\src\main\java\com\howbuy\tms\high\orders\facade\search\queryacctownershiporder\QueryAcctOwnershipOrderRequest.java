package com.howbuy.tms.high.orders.facade.search.queryacctownershiporder;

import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description:查询用户股权订单信息入参
 * @Author: yun.lu
 * Date: 2023/8/16 15:35
 */
@Getter
@Setter
public class QueryAcctOwnershipOrderRequest extends OrderSearchBaseRequest {

    public QueryAcctOwnershipOrderRequest() {
        setTxCode(TxCodes.QUERY_ACCT_OWNERSHIP);
        this.setDisCode(DisCodeEnum.HM.getCode());
    }

    /**
     * 产品编码
     */
    private String fundCode;

    /**
     * 是否只股权转让的,0:不是;1:是
     */
    private String onlyTransfer;

}
