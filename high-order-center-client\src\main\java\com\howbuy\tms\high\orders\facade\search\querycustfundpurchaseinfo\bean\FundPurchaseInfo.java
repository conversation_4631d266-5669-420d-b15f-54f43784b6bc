package com.howbuy.tms.high.orders.facade.search.querycustfundpurchaseinfo.bean;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 * @description: 基金购买信息对象
 * @author: hongdong.xie
 * @date: 2025-03-20 13:19:43
 * @since JDK 1.8
 */
public class FundPurchaseInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 基金代码
     */
    private String fundCode;
    
    /**
     * 基金名称
     */
    private String fundName;
    
    /**
     * 是否已购买
     */
    private Boolean hasBought;
    
    /**
     * 最低购买限额
     */
    private BigDecimal minPurchaseAmt;
    
    /**
     * 基金风险等级, 1-低风险,2-中低风险,3-中风险,4-中高风险,5-高风险
     */
    private String fundRiskLevel;
    
    /**
     * 基金风险等级描述
     */
    private String fundRiskLevelStr;

    /**
     * 币种  156-人民币、344-港元、392-日元、826-英镑、840-美元、978-欧元
     */
    private String currency;

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public Boolean getHasBought() {
        return hasBought;
    }

    public void setHasBought(Boolean hasBought) {
        this.hasBought = hasBought;
    }

    public BigDecimal getMinPurchaseAmt() {
        return minPurchaseAmt;
    }

    public void setMinPurchaseAmt(BigDecimal minPurchaseAmt) {
        this.minPurchaseAmt = minPurchaseAmt;
    }

    public String getFundRiskLevel() {
        return fundRiskLevel;
    }

    public void setFundRiskLevel(String fundRiskLevel) {
        this.fundRiskLevel = fundRiskLevel;
    }

    public String getFundRiskLevelStr() {
        return fundRiskLevelStr;
    }

    public void setFundRiskLevelStr(String fundRiskLevelStr) {
        this.fundRiskLevelStr = fundRiskLevelStr;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
    
    
} 