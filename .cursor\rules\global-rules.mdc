---
description: 
globs: 
alwaysApply: true
---
# 高端订单中心项目级别规范指南

## 项目概述
高端订单中心(high-order-center)是负责处理好买金融高端基金产品交易订单的核心系统，包含订单处理、查询、交易等功能。项目采用微服务架构，基于Spring Boot + Dubbo实现。

项目配置:
  编程语言: Java
  JDK版本: 1.8
  框架: Spring Boot 2.3.12, Spring Cloud Hoxton.SR12
  数据库: MySQL 8.0
  缓存: Redis
  数据库操作: Mybatis
  数据库连接池: Druid
  消息中间件: RocketMQ

### 模块划分
- **high-order-center-client**: 包含Dubbo接口定义和出入参定义
  - 基础包名: `com.howbuy.tms.high.orders`
  - 接口定义: `com.howbuy.tms.high.orders.facade`
  - 交易类接口: `com.howbuy.tms.high.orders.facade.trade`
  - 查询类接口: `com.howbuy.tms.high.orders.facade.search`
  - 枚举类: `com.howbuy.tms.high.orders.enums`
  - 接口入参: `com.howbuy.tms.high.orders.facade.*.request`
  - 接口出参: `com.howbuy.tms.high.orders.facade.*.response`

- **high-order-center-service**: 包含业务逻辑实现
  - 基础包名: `com.howbuy.tms.high.orders.service`
  - Dubbo接口实现: `com.howbuy.tms.high.orders.service.facade`
  - 业务实现: `com.howbuy.tms.high.orders.service.service`
  - 事务管理: `com.howbuy.tms.high.orders.service.repository`
  - 缓存服务: `com.howbuy.tms.high.orders.service.cacheservice`
  - 业务处理: `com.howbuy.tms.high.orders.service.business`
  - 切面处理: `com.howbuy.tms.high.orders.service.aspect`
  - 配置处理: `com.howbuy.tms.high.orders.service.config`
  - 事件处理: `com.howbuy.tms.high.orders.service.event`
  - 定时任务: `com.howbuy.tms.high.orders.service.job`

- **high-order-center-dao**: 包含数据库操作和ORM相关配置
  - 基础包名: `com.howbuy.tms.high.orders.dao`
  - 数据库操作: `com.howbuy.tms.high.orders.dao.mapper`
  - 数据库实体: `com.howbuy.tms.high.orders.dao.po`
  - 视图对象: `com.howbuy.tms.high.orders.dao.vo`

- **high-order-trade-remote**: 交易服务远程调用模块
  - 基础包名: `com.howbuy.tms.high.orders.trade.remote`
  - 启动类: `com.howbuy.tms.high.orders.trade.remote.HighOrderTradeApplication`
  - 配置目录: `src/main/resources/`

- **high-order-search-remote**: 查询服务远程调用模块
  - 基础包名: `com.howbuy.tms.high.orders.search.remote`
  - 启动类: `com.howbuy.tms.high.orders.search.remote.HighOrderSearchApplication`
  - 配置目录: `src/main/resources/`

## 命名规范

### 通用命名规则
- **类名**: 使用PascalCase（首字母大写的驼峰命名法），如`OrderService`、`DealOrderRepository`
- **方法名和变量名**: 使用camelCase（首字母小写的驼峰命名法），如`findOrderById`、`dealOrderNo`
- **常量**: 使用大写字母和下划线分隔，如`MAX_RETRY_COUNT`、`DEFAULT_TIMEOUT`
- **包名**: 全小写，使用点分隔，如`com.howbuy.tms.high.orders.service`

### 特定组件命名规则

#### 接口和实体类
- **Dubbo接口**: 以`Facade`结尾，如`QueryOrderFacade`、`SubsOrPurCounterFacade`
- **接口入参**: 接口名+`Request`，如`QueryOrderRequest`、`SubsOrPurCounterRequest`
- **接口出参**: 接口名+`Response`，如`QueryOrderResponse`、`SubsOrPurCounterResponse`
- **数据库实体**: 表名+`Po`/`PO`，如`HighDealOrderPo`、`CustProtocolPo`
- **业务对象**: 业务功能+`BO`，如`OrderCreateBO`、`TradeInfoBO`
- **视图对象**: 业务功能+`VO`，如`DealOrderVO`、`CustInfoVO`

#### 服务层组件
- **Dubbo实现类**: 接口名+`FacadeService`，如`QueryOrderFacadeService`、`SubsOrPurCounterFacadeService`
- **Service类**: 业务功能+`Service`，如`OrderCreateService`、`SequenceService`
- **Repository类**: 表名+`Repository`，如`DealOrderRepository`、`CustProtocolRepository`
- **Mapper接口**: 表名+`Mapper`，如`HighDealOrderMapper`、`CustBooksMapper`

## 接口定义规范

### Dubbo接口定义
1. 接口必须继承`BaseFacade<Request, Response>`
2. 接口必须使用APIDOC风格注释，包含以下内容：
   - API路径(`@api`)
   - API版本(`@apiVersion`)
   - API组(`@apiGroup`)
   - API名称(`@apiName`)
   - API描述(`@apiDescription`)
   - 请求参数(`@apiParam`)
   - 请求示例(`@apiParamExample`)
   - 响应结果(`@apiSuccess`)
   - 响应示例(`@apiSuccessExample`)

```java
/**
 * @api {DUBBO} com.howbuy.tms.high.orders.facade.trade.example.ExampleFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup ExampleFacadeImpl
 * @apiName execute()
 * @apiDescription 接口功能描述
 * @apiParam (请求体) {String} paramName 参数描述
 * @apiParamExample 请求体示例
 * {"paramName":"paramValue"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccessExample 响应结果示例
 * {"code":"0000","data":{},"description":"成功"}
 */
public interface ExampleFacade extends BaseFacade<ExampleRequest, ExampleResponse> {
}
```

### 入参/出参定义规范
1. 请求类必须继承`BaseRequest`或其他合适的基类
2. 响应类必须定义合理的返回结构
3. 所有字段必须有注释说明用途
4. 使用`@Getter`和`@Setter`注解（不使用`@Data`）
5. 字段命名遵循Java驼峰命名法
6. 敏感字段应使用合适的序列化/反序列化策略

```java
@Getter
@Setter
public class ExampleRequest extends BaseRequest {
    /**
     * 客户一账通号
     */
    private String txAcctNo;
    
    /**
     * 基金代码
     */
    private String fundCode;
}
```

## 接口实现规范

### Dubbo接口实现类
1. 实现类必须位于`com.howbuy.tms.high.orders.service.facade`包下，子包结构应与接口包保持一致
2. 实现类命名为接口名+`FacadeService`
3. 使用`@DubboService`注解暴露服务
4. 使用`@Service`注解将服务注册到Spring容器
5. 通过`@Autowired`或`@Resource`注入所需的Service类
6. 方法实现应简洁，主要负责参数校验和Service层的调用，不包含具体业务逻辑

```java
@DubboService
@Service("exampleFacade")
public class ExampleFacadeService implements ExampleFacade {
    @Autowired
    private ExampleService exampleService;
    
    @Override
    public ExampleResponse execute(ExampleRequest request) {
        // 参数校验
        
        // 调用业务服务处理
        return exampleService.process(request);
    }
}
```

## 服务层调用规范

### Service层
1. Service类应位于`com.howbuy.tms.high.orders.service.service`包下，根据业务功能划分子包
2. 使用`@Service`注解将服务注册到Spring容器
3. 使用`@Slf4j`或标准Logger对象进行日志记录
4. 通过`@Autowired`或`@Resource`注入Repository类和其他Service类
5. 实现具体业务逻辑，不直接操作数据库
6. 方法应有完整的注释

```java
@Service
public class ExampleService {
    private static final Logger logger = LogManager.getLogger(ExampleService.class);
    
    @Autowired
    private ExampleRepository exampleRepository;
    
    /**
     * @description: 业务处理方法
     * @param request 请求参数
     * @return 处理结果
     * @author: hongdong.xie
     * @date: 2025-03-20 13:20:55
     * @since JDK 1.8
     */
    public ExampleResponse process(ExampleRequest request) {
        // 业务逻辑实现
        return new ExampleResponse();
    }
}
```

### Repository层
1. Repository类应位于`com.howbuy.tms.high.orders.service.repository`包下
2. 使用`@Repository`注解将仓库注册到Spring容器
3. 使用`@Transactional`注解控制事务
4. 通过`@Autowired`或`@Resource`注入Mapper接口
5. 实现数据库操作逻辑，不包含业务逻辑
6. 方法应有完整的注释

```java
@Repository
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class ExampleRepository {
    @Autowired
    private ExampleMapper exampleMapper;
    
    /**
     * @description: 查询数据
     * @param id 主键ID
     * @return 数据实体
     * @author: hongdong.xie
     * @date: 2025-03-20 13:20:55
     * @since JDK 1.8
     */
    public ExamplePo queryById(Long id) {
        return exampleMapper.selectByPrimaryKey(id);
    }
    
    /**
     * @description: 保存数据
     * @param entity 数据实体
     * @return 影响行数
     * @author: hongdong.xie
     * @date: 2025-03-20 13:20:55
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int save(ExamplePo entity) {
        return exampleMapper.insert(entity);
    }
}
```

## 事务管理规范

### 事务注解使用原则
1. Repository层方法默认使用`@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)`
2. 数据修改操作使用`@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)`
3. 查询操作不需要修改默认的事务传播行为
4. 复杂业务场景可在Service层添加事务控制

### 事务传播行为说明
- `REQUIRED`: 如果当前存在事务，则加入该事务；如果当前没有事务，则创建一个新的事务
- `SUPPORTS`: 如果当前存在事务，则加入该事务；如果当前没有事务，则以非事务方式执行
- `MANDATORY`: 如果当前存在事务，则加入该事务；如果当前没有事务，则抛出异常
- `REQUIRES_NEW`: 创建一个新的事务，如果当前存在事务，则挂起当前事务
- `NOT_SUPPORTED`: 以非事务方式执行，如果当前存在事务，则挂起当前事务
- `NEVER`: 以非事务方式执行，如果当前存在事务，则抛出异常
- `NESTED`: 如果当前存在事务，则创建一个事务作为当前事务的嵌套事务来执行；如果当前没有事务，则等价于`REQUIRED`

## 注释规范

### 类注释
```java
/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 * @Description: 类功能描述
 * @Author: hongdong.xie
 * @Date: 2025-03-20 13:20:55
 * @since JDK 1.8
 */
```

### 方法注释
```java
/**
 * @description: 方法功能描述
 * @param paramName 参数说明
 * @return 返回值说明
 * @author: hongdong.xie
 * @date: 2025-03-20 13:20:55
 * @since JDK 1.8
 */
```

## 异常处理规范

1. 使用统一的异常处理机制
2. 业务异常应继承自`BusinessException`或应用的基础异常类
3. 合理使用自定义异常和错误码
4. 异常信息应包含足够的上下文信息，便于问题定位
5. 不要捕获异常后不处理或仅打印日志

```java
try {
    // 业务逻辑
} catch (BusinessException e) {
    logger.error("业务处理异常: {}", e.getMessage(), e);
    throw e;
} catch (Exception e) {
    logger.error("系统异常: {}", e.getMessage(), e);
    throw new SystemException(ExceptionCodes.SYSTEM_ERROR, "系统异常", e);
}
```

## 日志规范

1. 使用SLF4J + Log4j2进行日志记录
2. 日志级别合理使用:
   - ERROR: 系统错误，需要立即关注的问题
   - WARN: 潜在的问题，可能需要关注
   - INFO: 重要业务操作，可用于生产环境问题跟踪
   - DEBUG: 调试信息，仅在开发和测试环境使用

3. 日志内容应包含足够的上下文信息
4. 敏感信息不应记录到日志中
5. 使用占位符而非字符串拼接

```java
// 正确的做法
logger.info("处理订单, 订单号: {}, 客户号: {}", orderNo, txAcctNo);

// 错误的做法
logger.info("处理订单, 订单号: " + orderNo + ", 客户号: " + txAcctNo);
```

## 代码审查重点

在进行代码审查时，应重点关注以下方面：

1. **命名规范**: 类名、方法名、变量名是否符合规范
2. **接口定义**: Dubbo接口定义是否符合规范，包括注释、入参出参等
3. **接口实现**: 实现类是否位于正确的包下，是否使用了正确的注解
4. **服务层调用**: Service与Repository的职责是否分明，方法是否有必要的注释
5. **事务管理**: 是否正确使用了事务注解，事务传播行为是否合适
6. **异常处理**: 是否有统一的异常处理机制，是否合理使用了自定义异常
7. **日志规范**: 是否使用了正确的日志级别，日志内容是否合适

## 业务错误码规范

错误码应具有一定的结构，便于问题定位和排查。建议使用以下格式：

- 0000: 成功
- A开头: 系统级错误
- B开头: 业务级错误
- C开头: 接口调用错误
- D开头: 数据错误

每个错误码都应有详细的说明文档，便于开发和运维人员查阅。

## 安全规范

1. 敏感数据（如密码、证件号）需要加密存储
2. API调用需要进行身份验证和授权
3. 防止SQL注入、XSS等常见安全问题
4. 日志中不应包含敏感信息
5. 错误响应不应暴露系统内部信息

## 性能优化指南

1. 合理使用索引提高查询性能
2. 避免N+1查询问题
3. 使用批量操作替代循环单条操作
4. 使用缓存减少数据库访问
5. 大数据量处理时使用分页查询
6. 合理设置连接池参数
7. 使用异步处理提高并发能力
8. 对于并行处理任务，考虑使用线程池和CompletableFuture

## 常见问题处理

1. **并发控制**
   - 使用分布式锁控制并发访问
   - 使用乐观锁处理数据更新冲突
   - 关键业务操作使用悲观锁

2. **幂等性处理**
   - 生成唯一标识符作为幂等性控制
   - 使用状态检查防止重复操作
   - 通过Redis等实现分布式锁或标记

3. **缓存使用**
   - 根据业务特点选择合适的缓存策略
   - 处理缓存穿透、缓存击穿和缓存雪崩问题
   - 保持缓存与数据库的一致性

4. **跨服务调用**
   - 设置合理的超时时间和重试策略
   - 实现熔断和降级机制
   - 使用异步调用提高系统吞吐量

## 附录
- 常用状态码说明
- 数据库表关系图
- 服务依赖关系 