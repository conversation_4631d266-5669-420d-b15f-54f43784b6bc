/**
 *Copyright (c) 2016, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querybuyfundstatus;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * 
 * @description:(查询产品购买状态)
 * @reason:
 * <AUTHOR>
 * @date 2017年11月28日 上午9:14:32
 * @since JDK 1.6
 */
public interface QueryBuyFundStatusFacade extends BaseFacade<QueryBuyFundStatusRequest, QueryBuyFundStatusResponse> {

}
