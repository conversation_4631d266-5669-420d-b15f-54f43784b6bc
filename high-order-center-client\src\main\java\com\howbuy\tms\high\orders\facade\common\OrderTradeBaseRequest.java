/**
 *Copyright (c) 2016, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.common;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.validate.MyValidation;
/**
 * @description:(账户中心使用contractNo作为订单号，为了和基类中的dealNo加以区分，这里做一个设置)
 * @reason:
 * <AUTHOR>
 * @date 2017年4月1日 上午11:12:40
 * @since JDK 1.7
 */

/**
 * @apiDefine orderTradeBaseRequest 共有请求参数(req)
 * @apiGroup high-order-center
 * 
 * @apiParam {String} externalDealNo 外部订单号
 * @apiParam {String} txAcctNo 交易账号
 * 
 * 
 */

public class OrderTradeBaseRequest extends OrderBaseRequest {

    private static final long serialVersionUID = 4665983527827243789L;
    
    /**
     * 交易账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易账号", isRequired = true, max = 10)
    private String txAcctNo;
    
    /**
     * 一账通账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通账号", isRequired = false, max = 10)
    private String hbOneNo;
    
    /**
     * 外部订单号
     */
    @MyValidation(validatorType= ValidatorTypeEnum.String, fieldName = "外部订单号", isRequired = true, max=64)
    private String externalDealNo;
    
    public String getExternalDealNo() {
        return externalDealNo;
    }

    public void setExternalDealNo(String externalDealNo) {
        this.externalDealNo = externalDealNo;
    }
    
    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }
    
    public String getHbOneNo() {
        return hbOneNo;
    }

    public void setHbOneNo(String hbOneNo) {
        this.hbOneNo = hbOneNo;
    }

    /**
     * 
     * getShortAppTm:将时间的最后一位去掉，例如：121025-->12102(由于在极端情况下去掉最后一位时间是会错误拦截，
     * 所以目前直接返回申请时间)
     * 
     * @return
     * @return String
     * <AUTHOR>
     * @date 2016年10月26日 下午9:12:20
     */
    @Override
    protected String getShortAppTm(){
        return getAppTm();
        // String appTm = getAppTm();
        // if (appTm != null && !"".equals(appTm)) {
        // return appTm.substring(0, 5);
        // }
        // return appTm;
    }

}
