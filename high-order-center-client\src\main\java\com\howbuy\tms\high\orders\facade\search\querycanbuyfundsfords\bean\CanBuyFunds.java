/**
 * Copyright (c) 2022, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords.bean;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2022/5/5 16:06
 * @since JDK 1.8
 */
public class CanBuyFunds implements Serializable {

    private static final long serialVersionUID = -7324108424548157235L;
    /**
     * 管理人代码
     */
    private String fundManCode;
    /**
     * 可购买产品列表
     */
    private List<FundInfo> fundInfos;
    /**
     * 总记录数
     */
    private long totalCount;
    /**
     * 总页数
     */
    private Integer totalPage;
    /**
     * 当前页
     */
    private Integer pageNo;

    public String getFundManCode() {
        return fundManCode;
    }

    public void setFundManCode(String fundManCode) {
        this.fundManCode = fundManCode;
    }

    public List<FundInfo> getFundInfos() {
        return fundInfos;
    }

    public void setFundInfos(List<FundInfo> fundInfos) {
        this.fundInfos = fundInfos;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public long getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(Integer totalPage) {
        this.totalPage = totalPage;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public static class FundInfo implements Serializable{

        private static final long serialVersionUID = -5753414197821890570L;

        private String FundCode;

        private String FundName;

        public String getFundCode() {
            return FundCode;
        }

        public void setFundCode(String fundCode) {
            FundCode = fundCode;
        }

        public String getFundName() {
            return FundName;
        }

        public void setFundName(String fundName) {
            FundName = fundName;
        }
    }
}