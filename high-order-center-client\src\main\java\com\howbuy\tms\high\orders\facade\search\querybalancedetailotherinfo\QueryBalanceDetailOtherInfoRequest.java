package com.howbuy.tms.high.orders.facade.search.querybalancedetailotherinfo;

import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @Description:
 * @Author: yun.lu
 * Date: 2023/8/30 13:26
 */
@Getter
@Setter
public class QueryBalanceDetailOtherInfoRequest extends OrderSearchBaseRequest {
    /**
     * 产品code
     */
    private String fundCode;

    /**
     * 子产品代码list
     */
    private List<String> fundSubCodeList;

    /**
     * 分销渠道
     */
    private List<String> disCodeList;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.querybalancedetailotherinfo.QueryBalanceDetailOtherInfoFacade.execute(QueryBalanceDetailOtherInfoRequest request)
     * @apiName 查询持仓详情补充信息接口
     * @apiParam (request) {com.howbuy.tms.high.orders.facade.search.querybalancedetailotherinfo.QueryBalanceDetailOtherInfoRequest} request  请求参数
     * @apiParam (request) {String{<=10}} request.txAcctNo 交易账号
     * @apiParam (request) {String{<=10}} request.hbOneNo 一账通账号
     * @apiParam (request) {String{<=10}} request.fundCode 产品代码
     * @apiParam (request) {Array} request.fundSubCodeList 子产品代码列表
     * @apiParam (request) {Array} request.disCodeList 分销渠道列表
     * @apiSuccess {com.howbuy.tms.high.orders.facade.search.querybalancedetailotherinfo.QueryBalanceDetailOtherInfoResponse} QueryBalanceDetailOtherInfoResponse 响应结果
     * @apiSuccess (QueryBalanceDetailOtherInfoResponse) {String} fundCode 产品编码
     * @apiSuccess (QueryBalanceDetailOtherInfoResponse) {String} honeNo 一账通号
     * @apiSuccess (QueryBalanceDetailOtherInfoResponse) {String} txAcctNo 交易账号
     * @apiSuccess (QueryBalanceDetailOtherInfoResponse) {BigDecimal} accumIncome 累计收益(当前币种）
     * @apiSuccess (QueryBalanceDetailOtherInfoResponse) {BigDecimal} dailyAssetCurrency 日收益（当前币种）
     * @apiSuccess (QueryBalanceDetailOtherInfoResponse) {BigDecimal} dayAssetRate 最新收益率
     * @apiSuccess (QueryBalanceDetailOtherInfoResponse) {String} incomeLatestDay 最新收益日期
     * @apiSuccess (QueryBalanceDetailOtherInfoResponse) {Long} balanceDayNum 连续持有天数
     * @apiSuccess (QueryBalanceDetailOtherInfoResponse) {String} startHoldDay 开始持有日期
     * @apiSuccess (QueryBalanceDetailOtherInfoResponse) {String} nowDt 当前日期
     * @apiSuccess (QueryBalanceDetailOtherInfoResponse) {String} inHiddenConf 该产品在“特殊隐藏配置表”,1:有;0:没有
     * @apiSuccess (QueryBalanceDetailOtherInfoResponse) {String} inSpecialTransfer 该产品在“特殊产品交易类型转译表”内 ,1:有;0:没有
     * @apiSuccess (QueryBalanceDetailOtherInfoResponse) {Array} subBalanceDetailBaseInfoList 子产品详情信息列表
     */
    public QueryBalanceDetailOtherInfoRequest() {
        this.setTxCode(TxCodes.QUERY_BALANCE_OTHER_INFO);
        this.setDisCode(DisCodeEnum.HM.getCode());
    }
}
