/**
 * Copyright (c) 2022, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords.bean.CanBuyFunds;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2022/5/5 15:12
 * @since JDK 1.8
 */
public class QueryCanBuyFundsForDsResponse extends OrderSearchBaseResponse {

    private static final long serialVersionUID = -145791401200961743L;

    /**
     * 管理人可购买产品列表
     */
    private List<CanBuyFunds> canBuyFundsList;

    public List<CanBuyFunds> getCanBuyFundsList() {
        return canBuyFundsList;
    }

    public void setCanBuyFundsList(List<CanBuyFunds> canBuyFundsList) {
        this.canBuyFundsList = canBuyFundsList;
    }
}