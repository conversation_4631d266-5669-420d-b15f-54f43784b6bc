package com.howbuy.tms.high.orders.facade.search.queryacctbalance;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @Author: yun.lu
 * Date: 2023/6/29 11:08
 */
@Data
public class QueryDirectBalanceParam implements Serializable {
    /**
     * 一账通号
     */
    private String hbOneNo;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 是否香港
     */
    private String hkSaleFlag;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 产品子类型
     */
    private String productSubType;

    private List<String> crisisFundList;
    /**
     * 分销机构
     */
    private List<String> disCodeList;

    private String callType;
    /**
     * 是否查询持仓,1:只查询持仓,0:查询清仓的,null,查询所有
     */
    private String balanceStatus;

    /**
     * 交易账号
     */
    private String txAcctNo;


}
