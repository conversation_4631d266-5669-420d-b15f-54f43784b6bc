package com.howbuy.tms.high.orders.facade.search.queryCustBalanceFund;

import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

import java.util.List;

/**
 * @Description:查询用户持仓基金入参
 * @Author: yun.lu
 * Date: 2024/7/23 15:08
 */
public class QueryCustBalanceFundRequest extends OrderSearchBaseRequest {

    /**
     * 分销机构代码列表
     */
    private List<String> disCodeList;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryCustBalanceFund.QueryCustBalanceFundFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryCustBalanceFundFacade
     * @apiName execute
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode
     * @apiParam (请求参数) {String} outletCode
     * @apiParam (请求参数) {String} appDt
     * @apiParam (请求参数) {String} appTm
     * @apiParam (请求参数) {Number} pageNo
     * @apiParam (请求参数) {Number} pageSize
     * @apiParam (请求参数) {String} operIp
     * @apiParam (请求参数) {String} txCode
     * @apiParam (请求参数) {String} txChannel
     * @apiParam (请求参数) {String} dataTrack
     * @apiParam (请求参数) {String} subOutletCode
     * @apiParamExample 请求参数示例
     * hbOneNo=jCv&pageSize=1471&disCode=0hLkNhzL&txChannel=K2&appTm=A3HJ0KFo&disCodeList=p0k&subOutletCode=9cixM4XfVX&pageNo=5739&operIp=L1nEqdRf&txAcctNo=S&appDt=35&dataTrack=euC&txCode=S&outletCode=xBb
     * @apiSuccess (响应结果) {Array} fundCodeList 基金代码
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} description
     * @apiSuccess (响应结果) {Number} totalCount
     * @apiSuccess (响应结果) {Number} totalPage
     * @apiSuccess (响应结果) {Number} pageNo
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"JOxFfggGL9","fundCodeList":["2"],"totalPage":7162,"pageNo":2842,"description":"7naVe3SkBq","totalCount":2147}
     */
    public QueryCustBalanceFundRequest() {
        this.setTxCode(TxCodes.QUERY_CUSTOMER_BALANCE_FUND);
    }


    public List<String> getDisCodeList() {
        return disCodeList;
    }

    public void setDisCodeList(List<String> disCodeList) {
        this.disCodeList = disCodeList;
    }
}
