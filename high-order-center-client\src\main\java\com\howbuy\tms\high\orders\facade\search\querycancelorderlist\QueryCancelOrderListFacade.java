/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.facade.search.querycancelorderlist;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * @description:(查询高端可撤单列表) 
 * <AUTHOR>
 * @date 2017年4月25日 下午1:17:55
 * @since JDK 1.6
 */
public interface QueryCancelOrderListFacade extends BaseFacade<QueryCancelOrderListRequest, QueryCancelOrderListResponse>{

}

