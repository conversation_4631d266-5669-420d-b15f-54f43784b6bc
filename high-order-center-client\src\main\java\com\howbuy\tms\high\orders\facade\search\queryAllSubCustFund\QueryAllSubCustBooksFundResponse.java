package com.howbuy.tms.high.orders.facade.search.queryAllSubCustFund;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Data;

import java.util.List;

/**
 * @Description:查询持仓子账单明细中,有持仓的产品列表
 * @Author: yun.lu
 * Date: 2024/10/12 16:56
 */
@Data
public class QueryAllSubCustBooksFundResponse extends OrderSearchBaseResponse {
    /**
     * 产品列表
     */
    private List<String> fundCodeList;
}
