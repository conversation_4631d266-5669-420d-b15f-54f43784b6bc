package com.howbuy.tms.high.orders.facade.search.queryacctbalance;

import com.howbuy.tms.high.orders.facade.common.BaseDto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:(高端在途基金)
 * @reason:
 * <AUTHOR>
 * @date 2018年6月21日 下午9:06:36
 * @since JDK 1.7
 */
public class UnconfirmeProduct extends BaseDto {
    private static final long serialVersionUID = -5610418332175150805L;

    /**
     * 产品代码
     */
    private String fundCode;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 产品子类型
     */
    private String productSubType;
    /**
     * 待确认金额(人民币)
     */
    private BigDecimal unconfirmedAmt;
    /**
     * 好买香港代销标识: 0-否; 1-是
     */
    private String hkSaleFlag;

    /**
     * 销售渠道
     */
    private String disCode;

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getProductSubType() {
        return productSubType;
    }

    public void setProductSubType(String productSubType) {
        this.productSubType = productSubType;
    }

    public BigDecimal getUnconfirmedAmt() {
        return unconfirmedAmt;
    }

    public void setUnconfirmedAmt(BigDecimal unconfirmedAmt) {
        this.unconfirmedAmt = unconfirmedAmt;
    }

    public String getHkSaleFlag() {
        return hkSaleFlag;
    }

    public void setHkSaleFlag(String hkSaleFlag) {
        this.hkSaleFlag = hkSaleFlag;
    }
}
